// ID Scanning and Data Extraction Utilities
import * as faceapi from 'face-api.js';

/**
 * Enhanced ID scanning with OCR and data extraction
 */
export class IDScanner {
  constructor() {
    this.isInitialized = false;
    this.ocrWorker = null;
  }

  /**
   * Initialize OCR and face detection models
   */
  async initialize() {
    try {
      console.log('🔧 Initializing ID Scanner...');
      
      // Load face-api models for face detection and recognition
      await Promise.all([
        faceapi.nets.ssdMobilenetv1.loadFromUri('/models'),
        faceapi.nets.faceLandmark68Net.loadFromUri('/models'),
        faceapi.nets.faceRecognitionNet.loadFromUri('/models'),
        faceapi.nets.ageGenderNet.loadFromUri('/models')
      ]);

      // Initialize Tesseract.js for OCR (if available)
      if (window.Tesseract) {
        this.ocrWorker = await window.Tesseract.createWorker();
        await this.ocrWorker.loadLanguage('eng');
        await this.ocrWorker.initialize('eng');
        console.log('📖 OCR initialized successfully');
      } else {
        console.warn('⚠️ Tesseract.js not available, using mock OCR');
      }

      this.isInitialized = true;
      console.log('✅ ID Scanner initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize ID Scanner:', error);
      throw new Error('Failed to initialize ID scanning system');
    }
  }

  /**
   * Scan and extract data from ID document
   */
  async scanIDDocument(imageFile) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      console.log('🔍 Scanning ID document...');
      
      // Convert file to image element
      const img = await this.fileToImage(imageFile);
      
      // Extract text using OCR
      const extractedText = await this.extractTextFromImage(img);
      
      // Parse extracted data
      const parsedData = this.parseIDData(extractedText);
      
      // Extract face from ID
      const idFace = await this.extractFaceFromID(img);
      
      // Validate ID format and data
      const validation = this.validateIDData(parsedData);
      
      return {
        success: true,
        extractedData: parsedData,
        extractedText: extractedText,
        idFace: idFace,
        validation: validation,
        confidence: this.calculateConfidence(parsedData, validation, idFace)
      };
    } catch (error) {
      console.error('❌ ID scanning failed:', error);
      return {
        success: false,
        error: error.message,
        extractedData: null,
        extractedText: '',
        idFace: null,
        validation: { isValid: false, errors: [error.message] }
      };
    }
  }

  /**
   * Extract text from image using OCR
   */
  async extractTextFromImage(img) {
    try {
      if (this.ocrWorker) {
        console.log('📖 Extracting text using OCR...');
        const { data: { text } } = await this.ocrWorker.recognize(img);
        return text;
      } else {
        // Mock OCR for development/testing
        console.log('🔧 Using mock OCR extraction');
        return this.mockOCRExtraction();
      }
    } catch (error) {
      console.error('❌ OCR extraction failed:', error);
      return this.mockOCRExtraction();
    }
  }

  /**
   * Mock OCR extraction for testing
   */
  mockOCRExtraction() {
    const mockData = [
      'GOVERNMENT OF INDIA',
      'AADHAAR',
      'Name: JOHN DOE',
      'DOB: 15/08/1990',
      'Gender: MALE',
      'Address: 123 Main Street, City, State - 123456',
      'Aadhaar Number: 1234 5678 9012',
      'VID: 1234567890123456'
    ];
    return mockData.join('\n');
  }

  /**
   * Parse extracted text to structured data
   */
  parseIDData(extractedText) {
    const data = {
      name: null,
      dateOfBirth: null,
      gender: null,
      address: null,
      idNumber: null,
      idType: null,
      issueDate: null,
      expiryDate: null
    };

    const lines = extractedText.split('\n').map(line => line.trim());

    // Parse different ID types
    if (extractedText.includes('AADHAAR') || extractedText.includes('आधार')) {
      data.idType = 'AADHAAR';
      data.idNumber = this.extractAadhaarNumber(extractedText);
    } else if (extractedText.includes('DRIVING LICENCE') || extractedText.includes('DL')) {
      data.idType = 'DRIVING_LICENCE';
      data.idNumber = this.extractDLNumber(extractedText);
    } else if (extractedText.includes('PASSPORT')) {
      data.idType = 'PASSPORT';
      data.idNumber = this.extractPassportNumber(extractedText);
    } else if (extractedText.includes('VOTER') || extractedText.includes('EPIC')) {
      data.idType = 'VOTER_ID';
      data.idNumber = this.extractVoterIdNumber(extractedText);
    }

    // Extract common fields
    data.name = this.extractName(lines);
    data.dateOfBirth = this.extractDateOfBirth(lines);
    data.gender = this.extractGender(lines);
    data.address = this.extractAddress(lines);

    return data;
  }

  /**
   * Extract face from ID document
   */
  async extractFaceFromID(img) {
    try {
      console.log('👤 Extracting face from ID...');
      
      // Detect faces in the ID image
      const detections = await faceapi.detectAllFaces(img)
        .withFaceLandmarks()
        .withFaceDescriptors();

      if (detections.length === 0) {
        throw new Error('No face detected in ID document');
      }

      if (detections.length > 1) {
        console.warn('⚠️ Multiple faces detected in ID, using the largest one');
      }

      // Get the largest face (most likely the main ID photo)
      const mainFace = detections.reduce((largest, current) => {
        const largestArea = largest.detection.box.width * largest.detection.box.height;
        const currentArea = current.detection.box.width * current.detection.box.height;
        return currentArea > largestArea ? current : largest;
      });

      return {
        detection: mainFace.detection,
        landmarks: mainFace.landmarks,
        descriptor: mainFace.descriptor,
        confidence: mainFace.detection.score
      };
    } catch (error) {
      console.error('❌ Face extraction from ID failed:', error);
      return null;
    }
  }

  /**
   * Compare multiple photos for verification
   */
  async comparePhotos(profilePhoto, idPhoto, livePhoto) {
    try {
      console.log('🔄 Comparing photos for verification...');
      
      const results = {
        profileToId: null,
        profileToLive: null,
        idToLive: null,
        overallMatch: false,
        confidence: 0,
        details: {}
      };

      // Extract faces from all photos
      const [profileFace, idFace, liveFace] = await Promise.all([
        this.extractFaceFromImage(profilePhoto),
        this.extractFaceFromImage(idPhoto),
        this.extractFaceFromImage(livePhoto)
      ]);

      if (!profileFace || !idFace || !liveFace) {
        throw new Error('Could not detect faces in all provided images');
      }

      // Calculate similarity scores
      results.profileToId = this.calculateFaceSimilarity(profileFace.descriptor, idFace.descriptor);
      results.profileToLive = this.calculateFaceSimilarity(profileFace.descriptor, liveFace.descriptor);
      results.idToLive = this.calculateFaceSimilarity(idFace.descriptor, liveFace.descriptor);

      // Calculate overall confidence
      const scores = [results.profileToId, results.profileToLive, results.idToLive];
      results.confidence = scores.reduce((sum, score) => sum + score, 0) / scores.length;

      // Determine overall match (all comparisons should be above threshold)
      const threshold = 0.6; // 60% similarity threshold
      results.overallMatch = scores.every(score => score >= threshold);

      results.details = {
        profileFaceConfidence: profileFace.confidence,
        idFaceConfidence: idFace.confidence,
        liveFaceConfidence: liveFace.confidence,
        threshold: threshold,
        allScoresAboveThreshold: results.overallMatch
      };

      console.log('📊 Photo comparison results:', results);
      return results;
    } catch (error) {
      console.error('❌ Photo comparison failed:', error);
      return {
        profileToId: 0,
        profileToLive: 0,
        idToLive: 0,
        overallMatch: false,
        confidence: 0,
        error: error.message
      };
    }
  }

  /**
   * Extract face from any image
   */
  async extractFaceFromImage(imageFile) {
    const img = await this.fileToImage(imageFile);
    const detections = await faceapi.detectAllFaces(img)
      .withFaceLandmarks()
      .withFaceDescriptors();

    if (detections.length === 0) {
      throw new Error('No face detected in image');
    }

    // Return the most confident detection
    const bestDetection = detections.reduce((best, current) => 
      current.detection.score > best.detection.score ? current : best
    );

    return {
      detection: bestDetection.detection,
      landmarks: bestDetection.landmarks,
      descriptor: bestDetection.descriptor,
      confidence: bestDetection.detection.score
    };
  }

  /**
   * Calculate face similarity using descriptors
   */
  calculateFaceSimilarity(descriptor1, descriptor2) {
    const distance = faceapi.euclideanDistance(descriptor1, descriptor2);
    // Convert distance to similarity percentage (lower distance = higher similarity)
    const similarity = Math.max(0, (1 - distance) * 100);
    return Math.round(similarity);
  }

  /**
   * Convert file to image element
   */
  fileToImage(file) {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.onerror = reject;
      img.src = URL.createObjectURL(file);
    });
  }

  // Helper methods for data extraction
  extractName(lines) {
    for (const line of lines) {
      if (line.toLowerCase().includes('name:') || line.toLowerCase().includes('नाम:')) {
        return line.split(':')[1]?.trim() || null;
      }
    }
    return null;
  }

  extractDateOfBirth(lines) {
    for (const line of lines) {
      if (line.toLowerCase().includes('dob:') || line.toLowerCase().includes('date of birth:')) {
        return line.split(':')[1]?.trim() || null;
      }
    }
    return null;
  }

  extractGender(lines) {
    for (const line of lines) {
      if (line.toLowerCase().includes('gender:') || line.toLowerCase().includes('sex:')) {
        return line.split(':')[1]?.trim() || null;
      }
    }
    return null;
  }

  extractAddress(lines) {
    for (const line of lines) {
      if (line.toLowerCase().includes('address:') || line.toLowerCase().includes('पता:')) {
        return line.split(':')[1]?.trim() || null;
      }
    }
    return null;
  }

  extractAadhaarNumber(text) {
    const aadhaarRegex = /\d{4}\s?\d{4}\s?\d{4}/;
    const match = text.match(aadhaarRegex);
    return match ? match[0].replace(/\s/g, '') : null;
  }

  extractDLNumber(text) {
    const dlRegex = /[A-Z]{2}\d{2}\s?\d{11}/;
    const match = text.match(dlRegex);
    return match ? match[0].replace(/\s/g, '') : null;
  }

  extractPassportNumber(text) {
    const passportRegex = /[A-Z]\d{7}/;
    const match = text.match(passportRegex);
    return match ? match[0] : null;
  }

  extractVoterIdNumber(text) {
    const voterIdRegex = /[A-Z]{3}\d{7}/;
    const match = text.match(voterIdRegex);
    return match ? match[0] : null;
  }

  validateIDData(data) {
    const errors = [];
    const warnings = [];

    if (!data.name) {
      errors.push('Name not found in ID');
    }

    if (!data.idNumber) {
      errors.push('ID number not found');
    }

    if (!data.idType) {
      warnings.push('ID type could not be determined');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  calculateConfidence(parsedData, validation, idFace) {
    let confidence = 0;

    // Base confidence from data extraction
    if (parsedData.name) confidence += 20;
    if (parsedData.idNumber) confidence += 30;
    if (parsedData.dateOfBirth) confidence += 15;
    if (parsedData.gender) confidence += 10;
    if (parsedData.address) confidence += 10;

    // Face detection confidence
    if (idFace && idFace.confidence > 0.8) {
      confidence += 15;
    } else if (idFace && idFace.confidence > 0.6) {
      confidence += 10;
    }

    return Math.min(100, confidence);
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    if (this.ocrWorker) {
      await this.ocrWorker.terminate();
      this.ocrWorker = null;
    }
  }
}

// Create singleton instance
export const idScanner = new IDScanner();

// Export utility functions
export const scanIDDocument = (imageFile) => idScanner.scanIDDocument(imageFile);
export const comparePhotos = (profilePhoto, idPhoto, livePhoto) => 
  idScanner.comparePhotos(profilePhoto, idPhoto, livePhoto);
