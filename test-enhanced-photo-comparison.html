<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Photo Comparison & ID Data Recovery</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .upload-section {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .upload-box {
            border: 2px dashed #ddd;
            padding: 20px;
            text-align: center;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .upload-box:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .upload-box.has-image {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .preview-image {
            max-width: 100%;
            max-height: 200px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .analysis-section {
            margin: 30px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-item {
            text-align: center;
            padding: 15px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .similarity-score {
            font-size: 32px;
            font-weight: bold;
            margin: 10px 0;
        }
        .score-excellent { color: #28a745; }
        .score-good { color: #17a2b8; }
        .score-fair { color: #ffc107; }
        .score-poor { color: #dc3545; }
        .data-extraction {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .extracted-data {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 15px 0;
        }
        .data-field {
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 5px;
        }
        .quality-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .metric-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            margin: 5px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        button:hover:not(:disabled) {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .analyze-btn {
            background-color: #28a745;
            font-size: 18px;
            padding: 15px 30px;
            margin: 20px 0;
        }
        .analyze-btn:hover {
            background-color: #218838;
        }
        .results-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            display: none;
        }
        .results-section.show {
            display: block;
        }
        .loading {
            text-align: center;
            padding: 40px;
            font-size: 18px;
            color: #6c757d;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .histogram-canvas {
            border: 1px solid #ddd;
            margin: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Enhanced Photo Comparison & ID Data Recovery</h1>
        <p>Upload three images to test advanced photo comparison and ID data extraction capabilities.</p>

        <div class="upload-section">
            <div class="upload-box" id="profileBox">
                <h3>📸 Profile Photo</h3>
                <input type="file" id="profileInput" accept="image/*" style="display: none;">
                <button onclick="document.getElementById('profileInput').click()">Choose Profile Photo</button>
                <div id="profilePreview"></div>
            </div>
            
            <div class="upload-box" id="idBox">
                <h3>🆔 ID Document</h3>
                <input type="file" id="idInput" accept="image/*" style="display: none;">
                <button onclick="document.getElementById('idInput').click()">Choose ID Document</button>
                <div id="idPreview"></div>
            </div>
            
            <div class="upload-box" id="liveBox">
                <h3>📹 Live Photo</h3>
                <input type="file" id="liveInput" accept="image/*" style="display: none;">
                <button onclick="document.getElementById('liveInput').click()">Choose Live Photo</button>
                <div id="livePreview"></div>
            </div>
        </div>

        <button class="analyze-btn" id="analyzeBtn" onclick="performAnalysis()" disabled>
            🔬 Analyze Photos & Extract Data
        </button>

        <div class="results-section" id="resultsSection">
            <h2>📊 Analysis Results</h2>
            
            <div class="data-extraction" id="dataExtraction">
                <h3>🔍 Extracted ID Data</h3>
                <div class="extracted-data" id="extractedDataGrid"></div>
            </div>

            <div class="analysis-section">
                <h3>📈 Image Quality Analysis</h3>
                <div class="quality-metrics" id="qualityMetrics"></div>
            </div>

            <div class="analysis-section">
                <h3>👥 Photo Comparison Results</h3>
                <div class="comparison-grid" id="comparisonResults"></div>
            </div>

            <div class="analysis-section">
                <h3>📊 Detailed Analysis</h3>
                <div id="detailedAnalysis"></div>
            </div>
        </div>
    </div>

    <script type="module">
        import { idScanner } from './src/utils/idScanningUtils.js';

        let profileFile = null;
        let idFile = null;
        let liveFile = null;

        // File input handlers
        document.getElementById('profileInput').addEventListener('change', function(e) {
            profileFile = e.target.files[0];
            showPreview('profilePreview', 'profileBox', profileFile);
            checkAllFilesUploaded();
        });

        document.getElementById('idInput').addEventListener('change', function(e) {
            idFile = e.target.files[0];
            showPreview('idPreview', 'idBox', idFile);
            checkAllFilesUploaded();
        });

        document.getElementById('liveInput').addEventListener('change', function(e) {
            liveFile = e.target.files[0];
            showPreview('livePreview', 'liveBox', liveFile);
            checkAllFilesUploaded();
        });

        function showPreview(previewId, boxId, file) {
            const preview = document.getElementById(previewId);
            const box = document.getElementById(boxId);
            
            if (file) {
                const img = document.createElement('img');
                img.src = URL.createObjectURL(file);
                img.className = 'preview-image';
                img.onload = () => URL.revokeObjectURL(img.src);
                
                preview.innerHTML = '';
                preview.appendChild(img);
                
                const info = document.createElement('div');
                info.innerHTML = `
                    <small>
                        ${file.name}<br>
                        ${(file.size / 1024 / 1024).toFixed(2)} MB
                    </small>
                `;
                preview.appendChild(info);
                
                box.classList.add('has-image');
            }
        }

        function checkAllFilesUploaded() {
            const analyzeBtn = document.getElementById('analyzeBtn');
            analyzeBtn.disabled = !(profileFile && idFile && liveFile);
        }

        window.performAnalysis = async function() {
            const resultsSection = document.getElementById('resultsSection');
            const analyzeBtn = document.getElementById('analyzeBtn');
            
            analyzeBtn.disabled = true;
            analyzeBtn.innerHTML = '🔄 Analyzing...';
            resultsSection.classList.add('show');
            resultsSection.innerHTML = '<div class="loading">🔄 Performing comprehensive analysis...</div>';

            try {
                // Initialize ID scanner
                await idScanner.initialize();

                // Step 1: Extract data from ID
                console.log('🔍 Extracting data from ID document...');
                const idScanResult = await idScanner.scanIDDocument(idFile);
                
                // Step 2: Compare photos
                console.log('📊 Comparing photos...');
                const comparisonResult = await idScanner.comparePhotos(profileFile, idFile, liveFile);

                // Display results
                displayResults(idScanResult, comparisonResult);

            } catch (error) {
                console.error('Analysis failed:', error);
                resultsSection.innerHTML = `<div class="error">❌ Analysis failed: ${error.message}</div>`;
            } finally {
                analyzeBtn.disabled = false;
                analyzeBtn.innerHTML = '🔬 Analyze Photos & Extract Data';
            }
        };

        function displayResults(idScanResult, comparisonResult) {
            const resultsSection = document.getElementById('resultsSection');
            
            resultsSection.innerHTML = `
                <h2>📊 Analysis Results</h2>
                
                <div class="data-extraction">
                    <h3>🔍 Extracted ID Data</h3>
                    <div class="extracted-data">
                        ${displayExtractedData(idScanResult)}
                    </div>
                </div>

                <div class="analysis-section">
                    <h3>📈 Image Quality Analysis</h3>
                    <div class="quality-metrics">
                        ${displayQualityMetrics(comparisonResult.imageAnalysis)}
                    </div>
                </div>

                <div class="analysis-section">
                    <h3>👥 Photo Comparison Results</h3>
                    <div class="comparison-grid">
                        ${displayComparisonResults(comparisonResult)}
                    </div>
                </div>

                <div class="analysis-section">
                    <h3>📊 Detailed Analysis</h3>
                    <div class="success">
                        <strong>Overall Match:</strong> ${comparisonResult.overallMatch ? '✅ YES' : '❌ NO'}<br>
                        <strong>Confidence:</strong> ${Math.round(comparisonResult.confidence)}%<br>
                        <strong>Detection Method:</strong> ${comparisonResult.details.faceDetectionMethod || 'Enhanced Analysis'}
                    </div>
                    <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;">
${JSON.stringify({ idScanResult, comparisonResult }, null, 2)}
                    </pre>
                </div>
            `;
        }

        function displayExtractedData(idScanResult) {
            if (!idScanResult.success) {
                return '<div class="error">❌ Failed to extract data from ID</div>';
            }

            const data = idScanResult.extractedData;
            return `
                <div class="data-field"><strong>Name:</strong> ${data.name || 'Not found'}</div>
                <div class="data-field"><strong>ID Type:</strong> ${data.idType || 'Unknown'}</div>
                <div class="data-field"><strong>ID Number:</strong> ${data.idNumber || 'Not found'}</div>
                <div class="data-field"><strong>Date of Birth:</strong> ${data.dateOfBirth || 'Not found'}</div>
                <div class="data-field"><strong>Gender:</strong> ${data.gender || 'Not found'}</div>
                <div class="data-field"><strong>Address:</strong> ${data.address || 'Not found'}</div>
                <div class="data-field"><strong>Extraction Confidence:</strong> ${idScanResult.confidence}%</div>
                <div class="data-field"><strong>Processing Time:</strong> ${idScanResult.processingTime || 'N/A'}ms</div>
            `;
        }

        function displayQualityMetrics(imageAnalysis) {
            if (!imageAnalysis) {
                return '<div class="metric-card">Quality analysis not available</div>';
            }

            const labels = ['Profile', 'ID Document', 'Live Photo'];
            return imageAnalysis.individual.map((quality, index) => `
                <div class="metric-card">
                    <h4>${labels[index]}</h4>
                    <div class="metric-value ${getQualityClass(quality.quality)}">${Math.round(quality.quality)}%</div>
                    <div>Resolution: ${quality.resolution}</div>
                    <div>Brightness: ${Math.round(quality.brightness)}</div>
                    <div>Contrast: ${Math.round(quality.contrast)}</div>
                    <div>Sharpness: ${Math.round(quality.sharpness)}</div>
                </div>
            `).join('');
        }

        function displayComparisonResults(comparisonResult) {
            const comparisons = [
                { label: 'Profile ↔ ID', score: comparisonResult.profileToId },
                { label: 'Profile ↔ Live', score: comparisonResult.profileToLive },
                { label: 'ID ↔ Live', score: comparisonResult.idToLive }
            ];

            return comparisons.map(comp => `
                <div class="comparison-item">
                    <h4>${comp.label}</h4>
                    <div class="similarity-score ${getScoreClass(comp.score)}">${comp.score}%</div>
                    <div>${comp.score >= 60 ? '✅ Match' : '❌ No Match'}</div>
                </div>
            `).join('');
        }

        function getQualityClass(score) {
            if (score >= 80) return 'score-excellent';
            if (score >= 60) return 'score-good';
            if (score >= 40) return 'score-fair';
            return 'score-poor';
        }

        function getScoreClass(score) {
            if (score >= 80) return 'score-excellent';
            if (score >= 70) return 'score-good';
            if (score >= 60) return 'score-fair';
            return 'score-poor';
        }
    </script>
</body>
</html>
